'use client';

import { motion } from 'framer-motion';
import { Mail, Phone, MapPin, Clock } from 'lucide-react';
import useScrollReveal from '../../lib/hooks/useScrollReveal';

const ContactPage = () => {
  const heroReveal = useScrollReveal({ direction: 'up', delay: 0.2 });

  const contactInfo = [
    {
      icon: <Mail size={24} />,
      title: 'Email Us',
      details: '<EMAIL>',
      subDetails: 'We respond within 24 hours',
    },
    {
      icon: <Phone size={24} />,
      title: 'Call Us',
      details: '(*************',
      subDetails: 'Mon-Fri, 9AM-5PM EST',
    },
    {
      icon: <MapPin size={24} />,
      title: 'Visit Us',
      details: '123 Youth Center Blvd',
      subDetails: 'Community City, CC 12345',
    },
    {
      icon: <Clock size={24} />,
      title: 'Office Hours',
      details: 'Monday - Friday',
      subDetails: '9:00 AM - 5:00 PM EST',
    },
  ];

  return (
    <div className="min-h-screen">
      <section className="relative py-20 bg-gradient-to-br from-dark-bg via-dark-bg to-electric-blue/10 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            ref={heroReveal.ref}
            initial={heroReveal.initial}
            animate={heroReveal.controls}
            className="text-center"
          >
            <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
              Get in <span className="text-electric-blue">Touch</span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Have questions about our programs? Want to get involved? We would love to hear from you! 
              Reach out and let us start a conversation about how we can work together.
            </p>
          </motion.div>
        </div>
      </section>

      <section className="py-20 bg-dark-bg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {contactInfo.map((info, index) => (
              <motion.div
                key={info.title}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{ y: -5 }}
                className="group"
              >
                <div className="relative p-6 rounded-2xl bg-dark-bg border-2 border-electric-blue transition-all duration-300 hover:shadow-2xl hover:shadow-electric-blue/20 backdrop-blur-sm">
                  <div className="w-16 h-16 bg-electric-blue rounded-2xl flex items-center justify-center mb-4 text-dark-bg group-hover:scale-110 transition-transform duration-300">
                    {info.icon}
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2">{info.title}</h3>
                  <p className="text-electric-blue font-semibold mb-1">{info.details}</p>
                  <p className="text-gray-400 text-sm">{info.subDetails}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default ContactPage;
