'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  Facebook, 
  Instagram, 
  Twitter, 
  Youtube, 
  Mail, 
  Phone, 
  MapPin,
  Heart
} from 'lucide-react';

const Footer = () => {
  const quickLinks = [
    { href: '/', label: 'Home' },
    { href: '/about', label: 'About Us' },
    { href: '/programs', label: 'Programs' },
    { href: '/mentorship', label: 'Mentorship' },
  ];

  const programs = [
    { href: '/programs/youth-conferences', label: 'Youth Conferences' },
    { href: '/programs/leadership-workshops', label: 'Leadership Workshops' },
    { href: '/programs/community-projects', label: 'Community Projects' },
    { href: '/programs/skills-development', label: 'Skills Development' },
  ];

  const socialLinks = [
    { icon: Facebook, href: '#', label: 'Facebook', color: 'hover:text-blue-500' },
    { icon: Instagram, href: '#', label: 'Instagram', color: 'hover:text-hot-pink' },
    { icon: Twitter, href: '#', label: 'Twitter', color: 'hover:text-electric-blue' },
    { icon: Youtube, href: '#', label: 'YouTube', color: 'hover:text-cyber-red' },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <footer className="bg-dark-bg text-white">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* About Section */}
          <motion.div variants={itemVariants} className="lg:col-span-2">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center">
                <span className="text-dark-bg font-bold text-sm">NG</span>
              </div>
              <span className="text-white font-bold text-xl tracking-wide">
                NEXT GEN
              </span>
            </div>
            <p className="text-gray-300 mb-6 leading-relaxed">
              Empowering tomorrow's leaders through faith-based youth development, 
              mentorship, and community engagement. Discover your God-given potential 
              and make a lasting impact.
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social) => (
                <motion.a
                  key={social.label}
                  href={social.href}
                  whileHover={{ scale: 1.2, rotate: 5 }}
                  whileTap={{ scale: 0.9 }}
                  className={`text-gray-400 ${social.color} transition-colors duration-300`}
                  aria-label={social.label}
                >
                  <social.icon size={24} />
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* Quick Links */}
          <motion.div variants={itemVariants}>
            <h3 className="text-lg font-semibold mb-4 text-electric-blue">Quick Links</h3>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-neon-green transition-colors duration-300"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
              <li>
                <Link
                  href="/team"
                  className="text-gray-300 hover:text-neon-green transition-colors duration-300"
                >
                  Meet the Team
                </Link>
              </li>
              <li>
                <Link
                  href="/contact"
                  className="text-gray-300 hover:text-neon-green transition-colors duration-300"
                >
                  Contact Us
                </Link>
              </li>
            </ul>
          </motion.div>

          {/* Programs */}
          <motion.div variants={itemVariants}>
            <h3 className="text-lg font-semibold mb-4 text-bright-orange">Programs</h3>
            <ul className="space-y-2">
              {programs.map((program) => (
                <li key={program.href}>
                  <Link
                    href={program.href}
                    className="text-gray-300 hover:text-neon-green transition-colors duration-300"
                  >
                    {program.label}
                  </Link>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Contact Info */}
          <motion.div variants={itemVariants}>
            <h3 className="text-lg font-semibold mb-4 text-hot-pink">Contact</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Mail className="text-electric-blue" size={18} />
                <span className="text-gray-300"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="text-neon-green" size={18} />
                <span className="text-gray-300">(*************</span>
              </div>
              <div className="flex items-start space-x-3">
                <MapPin className="text-bright-orange mt-1" size={18} />
                <span className="text-gray-300">
                  123 Youth Center Blvd<br />
                  Community City, CC 12345
                </span>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Bottom Section */}
        <motion.div
          variants={itemVariants}
          className="border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center"
        >
          <p className="text-gray-400 text-sm mb-4 md:mb-0">
            © 2024 NextGen Youth Movement. All rights reserved.
          </p>
          <div className="flex items-center space-x-1 text-gray-400 text-sm">
            <span>Made with</span>
            <Heart className="text-cyber-red" size={16} />
            <span>for the next generation</span>
          </div>
        </motion.div>
      </motion.div>
    </footer>
  );
};

export default Footer;
