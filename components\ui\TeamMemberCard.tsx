'use client';

import { useState } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { Linkedin, Twitter, Mail, Phone } from 'lucide-react';

interface SocialLink {
  platform: 'linkedin' | 'twitter' | 'email' | 'phone';
  url: string;
}

interface TeamMemberCardProps {
  name: string;
  role: string;
  bio: string;
  image: string;
  socialLinks?: SocialLink[];
  color: 'electric-blue' | 'neon-green' | 'bright-orange' | 'hot-pink' | 'electric-purple';
  achievements?: string[];
}

const TeamMemberCard = ({
  name,
  role,
  bio,
  image,
  socialLinks = [],
  color,
  achievements = []
}: TeamMemberCardProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const colorClasses = {
    'electric-blue': {
      text: 'text-electric-blue',
      bg: 'bg-electric-blue',
      border: 'border-electric-blue',
      glow: 'shadow-electric-blue/50',
      gradient: 'from-electric-blue/20 to-transparent',
    },
    'neon-green': {
      text: 'text-neon-green',
      bg: 'bg-neon-green',
      border: 'border-neon-green',
      glow: 'shadow-neon-green/50',
      gradient: 'from-neon-green/20 to-transparent',
    },
    'bright-orange': {
      text: 'text-bright-orange',
      bg: 'bg-bright-orange',
      border: 'border-bright-orange',
      glow: 'shadow-bright-orange/50',
      gradient: 'from-bright-orange/20 to-transparent',
    },
    'hot-pink': {
      text: 'text-hot-pink',
      bg: 'bg-hot-pink',
      border: 'border-hot-pink',
      glow: 'shadow-hot-pink/50',
      gradient: 'from-hot-pink/20 to-transparent',
    },
    'electric-purple': {
      text: 'text-electric-purple',
      bg: 'bg-electric-purple',
      border: 'border-electric-purple',
      glow: 'shadow-electric-purple/50',
      gradient: 'from-electric-purple/20 to-transparent',
    },
  };

  const currentColor = colorClasses[color];

  const getSocialIcon = (platform: string) => {
    switch (platform) {
      case 'linkedin':
        return <Linkedin size={18} />;
      case 'twitter':
        return <Twitter size={18} />;
      case 'email':
        return <Mail size={18} />;
      case 'phone':
        return <Phone size={18} />;
      default:
        return null;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      whileInView={{ opacity: 1, scale: 1 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}
      whileHover={{ y: -5 }}
      className="relative group cursor-pointer"
    >
      <div className={`
        relative p-6 rounded-2xl bg-dark-bg border-2 ${currentColor.border}
        transition-all duration-300 group-hover:shadow-2xl group-hover:${currentColor.glow}
        backdrop-blur-sm overflow-hidden
      `}>
        {/* Background Gradient */}
        <div className={`
          absolute inset-0 bg-gradient-to-br ${currentColor.gradient}
          opacity-0 group-hover:opacity-100 transition-opacity duration-300
        `} />

        {/* Content */}
        <div className="relative z-10">
          {/* Profile Image */}
          <div className="relative mb-4">
            <motion.div
              className={`
                relative w-24 h-24 mx-auto rounded-full overflow-hidden
                border-4 ${currentColor.border} group-hover:border-white
                transition-all duration-300
              `}
              whileHover={{ scale: 1.1 }}
            >
              <Image
                src={image}
                alt={name}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 96px, 96px"
              />
              
              {/* Overlay on hover */}
              <motion.div
                className={`
                  absolute inset-0 ${currentColor.bg} opacity-0 group-hover:opacity-20
                  transition-opacity duration-300
                `}
                initial={false}
              />
            </motion.div>

            {/* Role Badge */}
            <div className={`
              absolute -bottom-2 left-1/2 transform -translate-x-1/2
              px-3 py-1 rounded-full text-xs font-bold
              ${currentColor.bg} text-dark-bg whitespace-nowrap
            `}>
              {role}
            </div>
          </div>

          {/* Name */}
          <h3 className={`text-xl font-bold ${currentColor.text} text-center mb-2 mt-4`}>
            {name}
          </h3>

          {/* Bio Preview */}
          <p className="text-gray-300 text-sm text-center mb-4 line-clamp-3">
            {bio}
          </p>

          {/* Social Links */}
          {socialLinks.length > 0 && (
            <div className="flex justify-center space-x-3 mb-4">
              {socialLinks.map((social, index) => (
                <motion.a
                  key={index}
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`
                    p-2 rounded-full ${currentColor.bg} text-dark-bg
                    hover:scale-110 transition-transform duration-300
                  `}
                  whileHover={{ rotate: 360 }}
                  whileTap={{ scale: 0.9 }}
                >
                  {getSocialIcon(social.platform)}
                </motion.a>
              ))}
            </div>
          )}

          {/* Expand Button */}
          <motion.button
            onClick={() => setIsExpanded(!isExpanded)}
            className={`
              w-full py-2 px-4 rounded-xl border-2 ${currentColor.border}
              ${currentColor.text} hover:${currentColor.bg} hover:text-dark-bg
              transition-all duration-300 font-medium
            `}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {isExpanded ? 'Show Less' : 'Learn More'}
          </motion.button>
        </div>

        {/* Expanded Content */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="relative z-10 mt-4 pt-4 border-t border-gray-700"
            >
              {/* Full Bio */}
              <p className="text-gray-300 text-sm mb-4 leading-relaxed">
                {bio}
              </p>

              {/* Achievements */}
              {achievements.length > 0 && (
                <div>
                  <h4 className={`text-sm font-semibold ${currentColor.text} mb-2`}>
                    Key Achievements
                  </h4>
                  <ul className="space-y-1">
                    {achievements.map((achievement, index) => (
                      <motion.li
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="text-gray-400 text-xs flex items-start"
                      >
                        <span className={`${currentColor.text} mr-2 mt-1`}>•</span>
                        {achievement}
                      </motion.li>
                    ))}
                  </ul>
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Hover Glow Effect */}
        <div className={`
          absolute inset-0 rounded-2xl ${currentColor.bg} opacity-0 group-hover:opacity-10
          blur-xl transition-opacity duration-300 -z-10
        `} />
      </div>
    </motion.div>
  );
};

export default TeamMemberCard;
