'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { ArrowRight, Sparkles, Target, Users, Heart } from 'lucide-react';
import StatisticsCounter from '../ui/StatisticsCounter';
import ProgramCard from '../ui/ProgramCard';
import useScrollReveal from '../../lib/hooks/useScrollReveal';

const HomePage = () => {
  const heroReveal = useScrollReveal({ direction: 'up', delay: 0.2 });
  const statsReveal = useScrollReveal({ direction: 'up', delay: 0.4 });
  const programsReveal = useScrollReveal({ direction: 'up', delay: 0.6 });

  const stats = [
    { number: 500, label: 'Youth Impacted', color: 'electric-blue' as const, suffix: '+' },
    { number: 12, label: 'Active Programs', color: 'neon-green' as const },
    { number: 50, label: 'Mentors', color: 'bright-orange' as const, suffix: '+' },
    { number: 8, label: 'Communities Served', color: 'hot-pink' as const },
  ];

  const featuredPrograms = [
    {
      title: 'Youth Leadership Conference',
      description: 'Annual conference bringing together young leaders from across the region to develop leadership skills and network.',
      icon: <Target size={32} />,
      color: 'electric-blue' as const,
      href: '/programs/youth-conferences',
      duration: '3 Days',
      participants: '200+ Youth',
      nextSession: 'July 15-17, 2024',
      featured: true,
    },
    {
      title: 'Mentorship Program',
      description: 'One-on-one mentoring connecting experienced leaders with emerging young talents.',
      icon: <Users size={32} />,
      color: 'neon-green' as const,
      href: '/mentorship',
      duration: '6 Months',
      participants: '50+ Pairs',
      nextSession: 'Rolling Enrollment',
    },
    {
      title: 'Community Impact Projects',
      description: 'Hands-on service projects that make a real difference in local communities.',
      icon: <Heart size={32} />,
      color: 'bright-orange' as const,
      href: '/programs/community-projects',
      duration: 'Ongoing',
      participants: '100+ Volunteers',
      nextSession: 'Monthly',
    },
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-dark-bg via-dark-bg to-electric-blue/10">
        {/* Animated Background Elements */}
        <div className="absolute inset-0">
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-electric-blue rounded-full opacity-30"
              animate={{
                x: [0, 100, 0],
                y: [0, -100, 0],
                scale: [1, 1.5, 1],
              }}
              transition={{
                duration: 3 + i * 0.5,
                repeat: Infinity,
                ease: 'easeInOut',
              }}
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
            />
          ))}
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            ref={heroReveal.ref}
            initial={heroReveal.initial}
            animate={heroReveal.controls}
            className="space-y-8"
          >
            {/* Main Headline */}
            <motion.h1
              className="text-5xl md:text-7xl font-bold text-white leading-tight"
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              Empowering{' '}
              <span className="bg-gradient-to-r from-electric-blue via-neon-green to-bright-orange bg-clip-text text-transparent animate-pulse-color">
                Tomorrow's
              </span>{' '}
              Leaders
            </motion.h1>

            {/* Subtitle */}
            <motion.p
              className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
            >
              Discover your God-given potential through faith-based youth development, 
              mentorship, and community engagement that transforms lives and builds character.
            </motion.p>

            {/* CTA Buttons */}
            <motion.div
              className="flex flex-col sm:flex-row gap-6 justify-center items-center"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              <Link href="/programs">
                <motion.div
                  className="group inline-flex items-center space-x-3 px-8 py-4 bg-electric-blue text-dark-bg font-bold rounded-2xl text-lg hover:shadow-2xl hover:shadow-electric-blue/50 transition-all duration-300"
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Sparkles size={24} />
                  <span>Explore Programs</span>
                  <motion.div
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  >
                    <ArrowRight size={24} />
                  </motion.div>
                </motion.div>
              </Link>

              <Link href="/about">
                <motion.div
                  className="group inline-flex items-center space-x-3 px-8 py-4 border-2 border-neon-green text-neon-green font-bold rounded-2xl text-lg hover:bg-neon-green hover:text-dark-bg transition-all duration-300"
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <span>Our Story</span>
                  <ArrowRight size={24} />
                </motion.div>
              </Link>
            </motion.div>
          </motion.div>
        </div>

        {/* Scroll Indicator */}
        <motion.div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <div className="w-6 h-10 border-2 border-electric-blue rounded-full flex justify-center">
            <motion.div
              className="w-1 h-3 bg-electric-blue rounded-full mt-2"
              animate={{ y: [0, 12, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
            />
          </div>
        </motion.div>
      </section>

      {/* Statistics Section */}
      <section className="py-20 bg-gradient-to-r from-dark-bg to-dark-gray">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            ref={statsReveal.ref}
            initial={statsReveal.initial}
            animate={statsReveal.controls}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Our <span className="text-electric-blue">Impact</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              See the difference we're making in young lives across our communities
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.5 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <StatisticsCounter {...stat} />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Programs Section */}
      <section className="py-20 bg-dark-bg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            ref={programsReveal.ref}
            initial={programsReveal.initial}
            animate={programsReveal.controls}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Featured <span className="text-neon-green">Programs</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Discover opportunities to grow, learn, and make a difference
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredPrograms.map((program, index) => (
              <motion.div
                key={program.title}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.2 }}
              >
                <ProgramCard {...program} />
              </motion.div>
            ))}
          </div>

          <motion.div
            className="text-center mt-12"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.8 }}
          >
            <Link href="/programs">
              <motion.div
                className="inline-flex items-center space-x-3 px-8 py-4 border-2 border-bright-orange text-bright-orange font-bold rounded-2xl text-lg hover:bg-bright-orange hover:text-dark-bg transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <span>View All Programs</span>
                <ArrowRight size={24} />
              </motion.div>
            </Link>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
