'use client';

import { ReactNode } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { ArrowRight, Calendar, Users, Clock } from 'lucide-react';

interface ProgramCardProps {
  title: string;
  description: string;
  icon: ReactNode;
  color: 'electric-blue' | 'neon-green' | 'bright-orange' | 'hot-pink' | 'electric-purple';
  href: string;
  duration?: string;
  participants?: string;
  nextSession?: string;
  featured?: boolean;
}

const ProgramCard = ({
  title,
  description,
  icon,
  color,
  href,
  duration,
  participants,
  nextSession,
  featured = false
}: ProgramCardProps) => {
  const colorClasses = {
    'electric-blue': {
      text: 'text-electric-blue',
      bg: 'bg-electric-blue',
      border: 'border-electric-blue',
      glow: 'shadow-electric-blue/50',
      gradient: 'from-electric-blue/20 to-transparent',
    },
    'neon-green': {
      text: 'text-neon-green',
      bg: 'bg-neon-green',
      border: 'border-neon-green',
      glow: 'shadow-neon-green/50',
      gradient: 'from-neon-green/20 to-transparent',
    },
    'bright-orange': {
      text: 'text-bright-orange',
      bg: 'bg-bright-orange',
      border: 'border-bright-orange',
      glow: 'shadow-bright-orange/50',
      gradient: 'from-bright-orange/20 to-transparent',
    },
    'hot-pink': {
      text: 'text-hot-pink',
      bg: 'bg-hot-pink',
      border: 'border-hot-pink',
      glow: 'shadow-hot-pink/50',
      gradient: 'from-hot-pink/20 to-transparent',
    },
    'electric-purple': {
      text: 'text-electric-purple',
      bg: 'bg-electric-purple',
      border: 'border-electric-purple',
      glow: 'shadow-electric-purple/50',
      gradient: 'from-electric-purple/20 to-transparent',
    },
  };

  const currentColor = colorClasses[color];

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}
      whileHover={{ y: -10 }}
      className={`
        relative group cursor-pointer
        ${featured ? 'md:col-span-2 lg:col-span-1' : ''}
      `}
    >
      <div className={`
        relative h-full p-6 rounded-2xl bg-dark-bg border-2 ${currentColor.border}
        transition-all duration-300 group-hover:shadow-2xl group-hover:${currentColor.glow}
        backdrop-blur-sm overflow-hidden
        ${featured ? 'border-4' : ''}
      `}>
        {/* Featured Badge */}
        {featured && (
          <div className={`
            absolute top-4 right-4 px-3 py-1 rounded-full text-xs font-bold
            ${currentColor.bg} text-dark-bg
          `}>
            FEATURED
          </div>
        )}

        {/* Background Gradient */}
        <div className={`
          absolute inset-0 bg-gradient-to-br ${currentColor.gradient}
          opacity-0 group-hover:opacity-100 transition-opacity duration-300
        `} />

        {/* Content */}
        <div className="relative z-10">
          {/* Icon */}
          <motion.div
            className={`
              w-16 h-16 rounded-2xl ${currentColor.bg} flex items-center justify-center mb-6
              text-dark-bg
            `}
            whileHover={{ rotate: 360, scale: 1.1 }}
            transition={{ duration: 0.5 }}
          >
            {icon}
          </motion.div>

          {/* Title */}
          <h3 className={`text-xl font-bold ${currentColor.text} mb-3 group-hover:text-white transition-colors duration-300`}>
            {title}
          </h3>

          {/* Description */}
          <p className="text-gray-300 mb-6 leading-relaxed">
            {description}
          </p>

          {/* Program Details */}
          {(duration || participants || nextSession) && (
            <div className="space-y-2 mb-6">
              {duration && (
                <div className="flex items-center space-x-2 text-sm text-gray-400">
                  <Clock size={16} className={currentColor.text} />
                  <span>{duration}</span>
                </div>
              )}
              {participants && (
                <div className="flex items-center space-x-2 text-sm text-gray-400">
                  <Users size={16} className={currentColor.text} />
                  <span>{participants}</span>
                </div>
              )}
              {nextSession && (
                <div className="flex items-center space-x-2 text-sm text-gray-400">
                  <Calendar size={16} className={currentColor.text} />
                  <span>{nextSession}</span>
                </div>
              )}
            </div>
          )}

          {/* CTA Button */}
          <Link href={href}>
            <motion.div
              className={`
                inline-flex items-center space-x-2 px-6 py-3 rounded-xl
                ${currentColor.bg} text-dark-bg font-semibold
                transition-all duration-300 group-hover:shadow-lg
              `}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span>Learn More</span>
              <motion.div
                animate={{ x: [0, 5, 0] }}
                transition={{ duration: 1.5, repeat: Infinity }}
              >
                <ArrowRight size={18} />
              </motion.div>
            </motion.div>
          </Link>
        </div>

        {/* Hover Glow Effect */}
        <div className={`
          absolute inset-0 rounded-2xl ${currentColor.bg} opacity-0 group-hover:opacity-10
          blur-xl transition-opacity duration-300 -z-10
        `} />

        {/* Click Ripple Effect */}
        <motion.div
          className={`absolute inset-0 rounded-2xl ${currentColor.bg} opacity-0`}
          whileTap={{ opacity: [0, 0.3, 0], scale: [1, 1.05, 1] }}
          transition={{ duration: 0.3 }}
        />
      </div>
    </motion.div>
  );
};

export default ProgramCard;
