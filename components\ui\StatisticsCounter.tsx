'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, useInView, useMotionValue, useSpring } from 'framer-motion';

interface StatisticsCounterProps {
  number: number;
  label: string;
  color: 'electric-blue' | 'neon-green' | 'bright-orange' | 'hot-pink' | 'electric-purple';
  duration?: number;
  suffix?: string;
  prefix?: string;
  showProgress?: boolean;
}

const StatisticsCounter = ({
  number,
  label,
  color,
  duration = 2,
  suffix = '',
  prefix = '',
  showProgress = true
}: StatisticsCounterProps) => {
  const ref = useRef<HTMLDivElement>(null);
  const isInView = useInView(ref, { once: true, margin: '-100px' });
  const [hasAnimated, setHasAnimated] = useState(false);

  const motionValue = useMotionValue(0);
  const springValue = useSpring(motionValue, {
    damping: 60,
    stiffness: 100,
  });

  const [displayValue, setDisplayValue] = useState(0);

  useEffect(() => {
    if (isInView && !hasAnimated) {
      setHasAnimated(true);
      motionValue.set(number);
    }
  }, [isInView, hasAnimated, motionValue, number]);

  useEffect(() => {
    const unsubscribe = springValue.on('change', (latest) => {
      setDisplayValue(Math.round(latest));
    });

    return unsubscribe;
  }, [springValue]);

  const colorClasses = {
    'electric-blue': {
      text: 'text-electric-blue',
      bg: 'bg-electric-blue',
      border: 'border-electric-blue',
      glow: 'shadow-electric-blue/50',
    },
    'neon-green': {
      text: 'text-neon-green',
      bg: 'bg-neon-green',
      border: 'border-neon-green',
      glow: 'shadow-neon-green/50',
    },
    'bright-orange': {
      text: 'text-bright-orange',
      bg: 'bg-bright-orange',
      border: 'border-bright-orange',
      glow: 'shadow-bright-orange/50',
    },
    'hot-pink': {
      text: 'text-hot-pink',
      bg: 'bg-hot-pink',
      border: 'border-hot-pink',
      glow: 'shadow-hot-pink/50',
    },
    'electric-purple': {
      text: 'text-electric-purple',
      bg: 'bg-electric-purple',
      border: 'border-electric-purple',
      glow: 'shadow-electric-purple/50',
    },
  };

  const currentColor = colorClasses[color];
  const progressPercentage = (displayValue / number) * 100;

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, scale: 0.5 }}
      animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.5 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      whileHover={{ scale: 1.05 }}
      className="relative group cursor-pointer"
    >
      {/* Main Container */}
      <div className={`
        relative p-6 rounded-2xl bg-dark-bg border-2 ${currentColor.border}
        transition-all duration-300 group-hover:shadow-2xl group-hover:${currentColor.glow}
        backdrop-blur-sm
      `}>
        {/* Progress Ring */}
        {showProgress && (
          <div className="absolute inset-0 flex items-center justify-center">
            <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
              {/* Background Circle */}
              <circle
                cx="50"
                cy="50"
                r="45"
                fill="none"
                stroke="rgba(255,255,255,0.1)"
                strokeWidth="2"
              />
              {/* Progress Circle */}
              <motion.circle
                cx="50"
                cy="50"
                r="45"
                fill="none"
                stroke={`var(--${color})`}
                strokeWidth="3"
                strokeLinecap="round"
                strokeDasharray={`${2 * Math.PI * 45}`}
                initial={{ strokeDashoffset: 2 * Math.PI * 45 }}
                animate={
                  isInView
                    ? { strokeDashoffset: 2 * Math.PI * 45 * (1 - progressPercentage / 100) }
                    : { strokeDashoffset: 2 * Math.PI * 45 }
                }
                transition={{ duration: duration, ease: 'easeOut' }}
                className="drop-shadow-lg"
              />
            </svg>
          </div>
        )}

        {/* Content */}
        <div className="relative z-10 text-center">
          {/* Number */}
          <motion.div
            className={`text-4xl md:text-5xl font-bold ${currentColor.text} mb-2`}
            initial={{ scale: 0 }}
            animate={isInView ? { scale: 1 } : { scale: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            {prefix}{displayValue.toLocaleString()}{suffix}
          </motion.div>

          {/* Label */}
          <motion.div
            className="text-white font-medium text-sm md:text-base"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5, delay: 0.7 }}
          >
            {label}
          </motion.div>
        </div>

        {/* Hover Effect Particles */}
        <motion.div
          className={`absolute inset-0 rounded-2xl ${currentColor.bg} opacity-0 group-hover:opacity-10 transition-opacity duration-300`}
          initial={false}
        />

        {/* Glow Effect */}
        <div className={`
          absolute inset-0 rounded-2xl ${currentColor.bg} opacity-0 group-hover:opacity-20
          blur-xl transition-opacity duration-300 -z-10
        `} />
      </div>

      {/* Click Ripple Effect */}
      <motion.div
        className={`absolute inset-0 rounded-2xl ${currentColor.bg} opacity-0`}
        whileTap={{ opacity: [0, 0.3, 0], scale: [1, 1.1, 1] }}
        transition={{ duration: 0.3 }}
      />
    </motion.div>
  );
};

export default StatisticsCounter;
