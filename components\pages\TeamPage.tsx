'use client';

import { motion } from 'framer-motion';
import TeamMemberCard from '../ui/TeamMemberCard';
import useScrollReveal from '../../lib/hooks/useScrollReveal';

const TeamPage = () => {
  const heroReveal = useScrollReveal({ direction: 'up', delay: 0.2 });

  const teamMembers = [
    {
      name: '<PERSON>',
      role: 'Executive Director',
      bio: '<PERSON> has over 15 years of experience in youth development and nonprofit leadership. She is passionate about empowering young people to discover their God-given potential and make a positive impact in their communities.',
      image: '/api/placeholder/300/300',
      socialLinks: [
        { platform: 'linkedin' as const, url: '#' },
        { platform: 'email' as const, url: 'mailto:<EMAIL>' },
      ],
      color: 'electric-blue' as const,
      achievements: [
        'Led organization to 300% growth in program participation',
        'Recognized as Youth Advocate of the Year 2023',
        'Masters in Nonprofit Management from State University',
      ],
    },
    {
      name: '<PERSON>',
      role: 'Program Director',
      bio: '<PERSON> oversees all youth programs and ensures they meet the highest standards of excellence. His background in education and youth ministry brings a unique perspective to program development.',
      image: '/api/placeholder/300/300',
      socialLinks: [
        { platform: 'linkedin' as const, url: '#' },
        { platform: 'twitter' as const, url: '#' },
      ],
      color: 'neon-green' as const,
      achievements: [
        'Developed award-winning leadership curriculum',
        '10+ years in youth ministry and education',
        'Certified in Youth Development Best Practices',
      ],
    },
    {
      name: 'Emily Chen',
      role: 'Community Outreach Coordinator',
      bio: 'Emily builds partnerships with local organizations and coordinates community service projects. She believes in the power of collaboration to create lasting change.',
      image: '/api/placeholder/300/300',
      socialLinks: [
        { platform: 'linkedin' as const, url: '#' },
        { platform: 'email' as const, url: 'mailto:<EMAIL>' },
      ],
      color: 'bright-orange' as const,
      achievements: [
        'Established partnerships with 25+ local organizations',
        'Coordinated 100+ community service hours monthly',
        'BA in Social Work and Community Development',
      ],
    },
    {
      name: 'David Rodriguez',
      role: 'Mentorship Coordinator',
      bio: 'David manages our mentorship program, carefully matching mentors with mentees to create meaningful relationships that transform lives.',
      image: '/api/placeholder/300/300',
      socialLinks: [
        { platform: 'linkedin' as const, url: '#' },
        { platform: 'phone' as const, url: 'tel:************' },
      ],
      color: 'hot-pink' as const,
      achievements: [
        'Successfully matched 200+ mentor-mentee pairs',
        'Certified Professional Coach (CPC)',
        '95% satisfaction rate in mentorship program',
      ],
    },
    {
      name: 'Jessica Thompson',
      role: 'Youth Program Specialist',
      bio: 'Jessica works directly with youth participants, facilitating workshops and providing ongoing support. Her energy and passion inspire young people to reach their full potential.',
      image: '/api/placeholder/300/300',
      socialLinks: [
        { platform: 'instagram' as const, url: '#' },
        { platform: 'email' as const, url: 'mailto:<EMAIL>' },
      ],
      color: 'electric-purple' as const,
      achievements: [
        'Led 50+ leadership workshops',
        'Youth Development Certification',
        'Former NextGen program participant turned leader',
      ],
    },
    {
      name: 'Michael Brown',
      role: 'Operations Manager',
      bio: 'Michael ensures our organization runs smoothly behind the scenes, managing logistics, finances, and administrative operations with excellence.',
      image: '/api/placeholder/300/300',
      socialLinks: [
        { platform: 'linkedin' as const, url: '#' },
        { platform: 'email' as const, url: 'mailto:<EMAIL>' },
      ],
      color: 'cyber-red' as const,
      achievements: [
        'Streamlined operations for 40% efficiency improvement',
        'MBA in Nonprofit Management',
        '8+ years in organizational leadership',
      ],
    },
  ];

  return (
    <div className="min-h-screen">
      <section className="relative py-20 bg-gradient-to-br from-dark-bg via-dark-bg to-hot-pink/10 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            ref={heroReveal.ref}
            initial={heroReveal.initial}
            animate={heroReveal.controls}
            className="text-center"
          >
            <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
              Meet Our <span className="text-hot-pink">Team</span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Our dedicated team of leaders, mentors, and volunteers are passionate about 
              empowering the next generation to discover their potential and make a lasting impact.
            </p>
          </motion.div>
        </div>
      </section>

      <section className="py-20 bg-dark-bg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Leadership <span className="text-electric-blue">Team</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Meet the passionate leaders who guide our mission and vision
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {teamMembers.map((member, index) => (
              <motion.div
                key={member.name}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <TeamMemberCard {...member} />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-20 bg-gradient-to-r from-electric-blue/10 to-neon-green/10">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Join Our <span className="text-neon-green">Team</span>
            </h2>
            <p className="text-xl text-gray-300 mb-8 leading-relaxed">
              Are you passionate about youth development and making a difference? 
              We are always looking for dedicated individuals to join our mission.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <motion.a
                href="/contact"
                className="inline-flex items-center justify-center px-8 py-4 bg-electric-blue text-dark-bg font-bold rounded-2xl text-lg hover:shadow-2xl hover:shadow-electric-blue/50 transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                View Open Positions
              </motion.a>
              <motion.a
                href="/contact"
                className="inline-flex items-center justify-center px-8 py-4 border-2 border-neon-green text-neon-green font-bold rounded-2xl text-lg hover:bg-neon-green hover:text-dark-bg transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Volunteer With Us
              </motion.a>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default TeamPage;
