'use client';

import { motion } from 'framer-motion';
import { Heart, Target, Users, Lightbulb, Award, Globe } from 'lucide-react';
import StatisticsCounter from '../ui/StatisticsCounter';
import useScrollReveal from '../../lib/hooks/useScrollReveal';

const AboutPage = () => {
  const heroReveal = useScrollReveal({ direction: 'up', delay: 0.2 });
  const missionReveal = useScrollReveal({ direction: 'left', delay: 0.3 });
  const statsReveal = useScrollReveal({ direction: 'up', delay: 0.4 });
  const valuesReveal = useScrollReveal({ direction: 'up', delay: 0.5 });

  const stats = [
    { number: 500, label: 'Youth Impacted', color: 'electric-blue' as const, suffix: '+' },
    { number: 12, label: 'Active Programs', color: 'neon-green' as const },
    { number: 50, label: 'Mentors', color: 'bright-orange' as const, suffix: '+' },
    { number: 8, label: 'Communities Served', color: 'hot-pink' as const },
  ];

  const coreValues = [
    {
      icon: <Heart size={48} />,
      title: 'Faith-Centered',
      description: 'We believe every young person has God-given potential waiting to be discovered and developed.',
      color: 'cyber-red',
    },
    {
      icon: <Target size={48} />,
      title: 'Purpose-Driven',
      description: 'We help youth discover their unique calling and equip them with tools to make a lasting impact.',
      color: 'electric-blue',
    },
    {
      icon: <Users size={48} />,
      title: 'Community-Focused',
      description: 'We build strong relationships and foster environments where young leaders can thrive together.',
      color: 'neon-green',
    },
    {
      icon: <Lightbulb size={48} />,
      title: 'Innovation-Minded',
      description: 'We embrace creative approaches to youth development and leadership training.',
      color: 'bright-orange',
    },
    {
      icon: <Award size={48} />,
      title: 'Excellence-Oriented',
      description: 'We strive for the highest standards in everything we do, developing character alongside competence.',
      color: 'hot-pink',
    },
    {
      icon: <Globe size={48} />,
      title: 'Impact-Focused',
      description: 'We measure success by the positive change our youth create in their communities and beyond.',
      color: 'electric-purple',
    },
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-dark-bg via-dark-bg to-electric-blue/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            ref={heroReveal.ref}
            initial={heroReveal.initial}
            animate={heroReveal.controls}
            className="text-center"
          >
            <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
              About <span className="text-electric-blue">NextGen</span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              We are a faith-based youth organization dedicated to empowering the next generation 
              of leaders through mentorship, community engagement, and character development.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Mission & Vision Section */}
      <section className="py-20 bg-dark-bg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            {/* Mission */}
            <motion.div
              ref={missionReveal.ref}
              initial={missionReveal.initial}
              animate={missionReveal.controls}
              className="space-y-8"
            >
              <div>
                <h2 className="text-4xl font-bold text-white mb-6">
                  Our <span className="text-neon-green">Mission</span>
                </h2>
                <p className="text-lg text-gray-300 leading-relaxed mb-6">
                  To empower young people to discover their God-given potential and develop 
                  into confident, capable leaders who make a positive impact in their 
                  communities and beyond.
                </p>
                <p className="text-lg text-gray-300 leading-relaxed">
                  We provide comprehensive programs that combine faith-based principles 
                  with practical leadership training, mentorship opportunities, and 
                  hands-on community service experiences.
                </p>
              </div>

              <div>
                <h3 className="text-3xl font-bold text-white mb-4">
                  Our <span className="text-bright-orange">Vision</span>
                </h3>
                <p className="text-lg text-gray-300 leading-relaxed">
                  A generation of young leaders who are grounded in faith, equipped 
                  with skills, and passionate about creating positive change in the world.
                </p>
              </div>
            </motion.div>

            {/* Visual Element */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="relative"
            >
              <div className="relative w-full h-96 rounded-2xl bg-gradient-to-br from-electric-blue/20 to-neon-green/20 flex items-center justify-center">
                <div className="text-center space-y-4">
                  <div className="w-24 h-24 bg-white rounded-full flex items-center justify-center mx-auto mb-6">
                    <span className="text-dark-bg font-bold text-2xl">NG</span>
                  </div>
                  <h4 className="text-2xl font-bold text-white">NextGen Youth Movement</h4>
                  <p className="text-electric-blue font-semibold">Empowering Tomorrow's Leaders</p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-20 bg-gradient-to-r from-dark-bg to-dark-gray">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            ref={statsReveal.ref}
            initial={statsReveal.initial}
            animate={statsReveal.controls}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Our <span className="text-electric-blue">Impact</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Numbers that tell the story of transformation and growth
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.5 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <StatisticsCounter {...stat} />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Core Values Section */}
      <section className="py-20 bg-dark-bg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            ref={valuesReveal.ref}
            initial={valuesReveal.initial}
            animate={valuesReveal.controls}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Our <span className="text-neon-green">Core Values</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              The principles that guide everything we do
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {coreValues.map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{ y: -5 }}
                className="group"
              >
                <div className="relative p-8 rounded-2xl bg-dark-bg border-2 border-gray-700 hover:border-electric-blue transition-all duration-300 hover:shadow-2xl hover:shadow-electric-blue/20">
                  {/* Icon */}
                  <div className={`w-20 h-20 rounded-2xl bg-${value.color} flex items-center justify-center mb-6 text-dark-bg group-hover:scale-110 transition-transform duration-300`}>
                    {value.icon}
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-bold text-white mb-4 group-hover:text-electric-blue transition-colors duration-300">
                    {value.title}
                  </h3>
                  <p className="text-gray-300 leading-relaxed">
                    {value.description}
                  </p>

                  {/* Hover Effect */}
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-electric-blue/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-gradient-to-r from-electric-blue/10 to-neon-green/10">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Ready to Make a <span className="text-electric-blue">Difference</span>?
            </h2>
            <p className="text-xl text-gray-300 mb-8 leading-relaxed">
              Join our community of young leaders and discover your potential to create positive change.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <motion.a
                href="/programs"
                className="inline-flex items-center justify-center px-8 py-4 bg-electric-blue text-dark-bg font-bold rounded-2xl text-lg hover:shadow-2xl hover:shadow-electric-blue/50 transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Explore Programs
              </motion.a>
              <motion.a
                href="/contact"
                className="inline-flex items-center justify-center px-8 py-4 border-2 border-neon-green text-neon-green font-bold rounded-2xl text-lg hover:bg-neon-green hover:text-dark-bg transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Get Involved
              </motion.a>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default AboutPage;
