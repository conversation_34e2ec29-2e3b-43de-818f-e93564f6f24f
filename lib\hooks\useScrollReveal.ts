'use client';

import { useEffect, useRef } from 'react';
import { useInView, useAnimation } from 'framer-motion';

interface UseScrollRevealOptions {
  threshold?: number;
  triggerOnce?: boolean;
  delay?: number;
  duration?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
  distance?: number;
}

export const useScrollReveal = (options: UseScrollRevealOptions = {}) => {
  const {
    threshold = 0.1,
    triggerOnce = true,
    delay = 0,
    duration = 0.6,
    direction = 'up',
    distance = 50,
  } = options;

  const ref = useRef(null);
  const isInView = useInView(ref, { 
    threshold, 
    once: triggerOnce,
    margin: '-100px'
  });
  const controls = useAnimation();

  const getInitialPosition = () => {
    switch (direction) {
      case 'up':
        return { y: distance, opacity: 0 };
      case 'down':
        return { y: -distance, opacity: 0 };
      case 'left':
        return { x: distance, opacity: 0 };
      case 'right':
        return { x: -distance, opacity: 0 };
      default:
        return { y: distance, opacity: 0 };
    }
  };

  const getFinalPosition = () => {
    switch (direction) {
      case 'up':
      case 'down':
        return { y: 0, opacity: 1 };
      case 'left':
      case 'right':
        return { x: 0, opacity: 1 };
      default:
        return { y: 0, opacity: 1 };
    }
  };

  useEffect(() => {
    if (isInView) {
      controls.start({
        ...getFinalPosition(),
        transition: {
          duration,
          delay,
          ease: 'easeOut',
        },
      });
    } else if (!triggerOnce) {
      controls.start(getInitialPosition());
    }
  }, [isInView, controls, delay, duration, triggerOnce]);

  return {
    ref,
    controls,
    initial: getInitialPosition(),
    isInView,
  };
};

export default useScrollReveal;
