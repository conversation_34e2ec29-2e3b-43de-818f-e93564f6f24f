import { Mail, Contact, Calendar, Book } from 'lucide-react';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    about: [
      { name: 'Our Mission', href: '#about' },
      { name: 'Core Values', href: '#about' },
      { name: 'Success Stories', href: '#mentorship' },
      { name: 'Team', href: '#about' }
    ],
    programs: [
      { name: 'Leadership Workshops', href: '#programs' },
      { name: 'Community Projects', href: '#programs' },
      { name: 'Skills Development', href: '#programs' },
      { name: 'Youth Conferences', href: '#programs' }
    ],
    resources: [
      { name: 'Mentorship', href: '#mentorship' },
      { name: 'Program Calendar', href: '#contact' },
      { name: 'Parent Resources', href: '#contact' },
      { name: 'Alumni Network', href: '#contact' }
    ],
    contact: [
      { name: 'Get Involved', href: '#contact' },
      { name: 'Volunteer', href: '#contact' },
      { name: 'Partner With Us', href: '#contact' },
      { name: 'Donate', href: '#contact' }
    ]
  };

  return (
    <footer className="bg-gradient-to-t from-black to-gray-900 border-t border-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8 mb-12">
          
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center mr-4">
                <span className="text-black font-bold text-lg">NG</span>
              </div>
              <span className="text-white font-arvo font-bold text-xl">
                NextGen Youth Movement
              </span>
            </div>
            
            <p className="text-gray-400 mb-6 leading-relaxed">
              Empowering teenagers to discover and develop their God-given potential through 
              transformative leadership programs, mentorship, and community engagement.
            </p>

            {/* Newsletter Signup */}
            <div>
              <h4 className="text-white font-semibold mb-3">Stay Connected</h4>
              <div className="flex space-x-2">
                <input
                  type="email"
                  placeholder="Your email address"
                  className="flex-1 px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-electric-blue text-sm"
                />
                <button className="px-4 py-2 bg-gradient-to-r from-electric-blue to-bright-orange text-white rounded-lg font-semibold hover:scale-105 transition-transform duration-300 text-sm">
                  Join
                </button>
              </div>
            </div>
          </div>

          {/* Links Sections */}
          <div>
            <h4 className="text-white font-arvo font-bold mb-4">About Us</h4>
            <ul className="space-y-2">
              {footerLinks.about.map((link) => (
                <li key={link.name}>
                  <a
                    href={link.href}
                    className="text-gray-400 hover:text-electric-blue transition-colors duration-300 text-sm"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h4 className="text-white font-arvo font-bold mb-4">Programs</h4>
            <ul className="space-y-2">
              {footerLinks.programs.map((link) => (
                <li key={link.name}>
                  <a
                    href={link.href}
                    className="text-gray-400 hover:text-neon-green transition-colors duration-300 text-sm"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h4 className="text-white font-arvo font-bold mb-4">Resources</h4>
            <ul className="space-y-2">
              {footerLinks.resources.map((link) => (
                <li key={link.name}>
                  <a
                    href={link.href}
                    className="text-gray-400 hover:text-bright-orange transition-colors duration-300 text-sm"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h4 className="text-white font-arvo font-bold mb-4">Get Involved</h4>
            <ul className="space-y-2">
              {footerLinks.contact.map((link) => (
                <li key={link.name}>
                  <a
                    href={link.href}
                    className="text-gray-400 hover:text-hot-pink transition-colors duration-300 text-sm"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Contact Info Bar */}
        <div className="border-t border-gray-700 pt-8 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-electric-blue to-cyan-400 rounded-lg flex items-center justify-center">
                <Mail className="w-5 h-5 text-white" />
              </div>
              <div>
                <p className="text-white font-semibold text-sm">Email</p>
                <a href="mailto:<EMAIL>" className="text-gray-400 hover:text-electric-blue transition-colors duration-300 text-sm">
                  <EMAIL>
                </a>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-neon-green to-green-400 rounded-lg flex items-center justify-center">
                <Contact className="w-5 h-5 text-white" />
              </div>
              <div>
                <p className="text-white font-semibold text-sm">Phone</p>
                <a href="tel:******-NGY-MOVE" className="text-gray-400 hover:text-neon-green transition-colors duration-300 text-sm">
                  (555) NGY-MOVE
                </a>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-bright-orange to-orange-400 rounded-lg flex items-center justify-center">
                <Calendar className="w-5 h-5 text-white" />
              </div>
              <div>
                <p className="text-white font-semibold text-sm">Office Hours</p>
                <p className="text-gray-400 text-sm">Mon-Fri: 9AM-6PM</p>
              </div>
            </div>
          </div>
        </div>

        {/* Social Links & Stats */}
        <div className="border-t border-gray-700 pt-8 mb-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-6 md:space-y-0">
            
            {/* Social Links */}
            <div>
              <p className="text-white font-semibold mb-3 text-center md:text-left">Follow Our Impact</p>
              <div className="flex justify-center md:justify-start space-x-4">
                <a href="#" className="w-10 h-10 bg-electric-blue rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-300" aria-label="Facebook">
                  <span className="text-white font-bold text-sm">f</span>
                </a>
                <a href="#" className="w-10 h-10 bg-hot-pink rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-300" aria-label="Instagram">
                  <span className="text-white font-bold text-sm">ig</span>
                </a>
                <a href="#" className="w-10 h-10 bg-bright-orange rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-300" aria-label="YouTube">
                  <span className="text-white font-bold text-sm">yt</span>
                </a>
                <a href="#" className="w-10 h-10 bg-neon-green rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-300" aria-label="Twitter">
                  <span className="text-white font-bold text-sm">tw</span>
                </a>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="flex space-x-8 text-center">
              <div>
                <div className="text-2xl font-bold text-electric-blue">500+</div>
                <div className="text-gray-400 text-xs">Youth Served</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-neon-green">12</div>
                <div className="text-gray-400 text-xs">Programs</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-bright-orange">50+</div>
                <div className="text-gray-400 text-xs">Mentors</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-hot-pink">8</div>
                <div className="text-gray-400 text-xs">Communities</div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-700 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-gray-400 text-sm">
              © {currentYear} NextGen Youth Movement. All rights reserved.
            </p>
            
            <div className="flex space-x-6 text-sm">
              <a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">
                Privacy Policy
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">
                Terms of Service
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">
                Cookie Policy
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;


import { useState } from 'react';
import { Menu, X } from 'lucide-react';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);

  const navItems = [
    { name: 'Home', href: '#home' },
    { name: 'About', href: '#about' },
    { name: 'Programs', href: '#programs' },
    { name: 'Mentorship', href: '#mentorship' },
    { name: 'Contact', href: '#contact' },
  ];

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-black/90 backdrop-blur-md border-b border-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <div className="flex-shrink-0 flex items-center">
              <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center mr-3">
                <span className="text-black font-bold text-lg">NG</span>
              </div>
              <span className="text-white font-arvo font-bold text-xl">
                NextGen Youth Movement
              </span>
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-8">
              {navItems.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="text-gray-300 hover:text-white px-3 py-2 text-sm font-medium transition-colors duration-300 relative group"
                >
                  {item.name}
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-electric-blue transition-all duration-300 group-hover:w-full"></span>
                </a>
              ))}
            </div>
          </div>

          {/* CTA Button */}
          <div className="hidden md:block">
            <a
              href="#contact"
              className="btn-primary text-sm"
            >
              Join the Movement
            </a>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="text-gray-400 hover:text-white focus:outline-none focus:text-white transition-colors duration-300"
            >
              {isOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      <div className={`md:hidden transition-all duration-300 ${isOpen ? 'max-h-64 opacity-100' : 'max-h-0 opacity-0 overflow-hidden'}`}>
        <div className="px-2 pt-2 pb-3 space-y-1 bg-black/95 backdrop-blur-md border-t border-gray-800">
          {navItems.map((item) => (
            <a
              key={item.name}
              href={item.href}
              className="text-gray-300 hover:text-white block px-3 py-2 text-base font-medium transition-colors duration-300"
              onClick={() => setIsOpen(false)}
            >
              {item.name}
            </a>
          ))}
          <a
            href="#contact"
            className="btn-primary text-sm mt-4 inline-block"
            onClick={() => setIsOpen(false)}
          >
            Join the Movement
          </a>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;

import { useState } from 'react';
import { Star, Contact, Calendar, Book } from 'lucide-react';

const AboutSection = () => {
  const [activeValue, setActiveValue] = useState<number | null>(null);

  const coreValues = [
    {
      icon: <Star className="w-8 h-8" />,
      title: "God-Given Potential",
      description: "We believe every young person has unique gifts and talents waiting to be discovered and developed.",
      color: "electric-blue"
    },
    {
      icon: <Contact className="w-8 h-8" />,
      title: "Community Impact",
      description: "True leadership emerges through service to others and making a positive difference in our communities.",
      color: "neon-green"
    },
    {
      icon: <Calendar className="w-8 h-8" />,
      title: "Transformative Growth",
      description: "Personal transformation happens through intentional mentorship, challenges, and real-world application.",
      color: "bright-orange"
    },
    {
      icon: <Book className="w-8 h-8" />,
      title: "Lifelong Learning",
      description: "We foster a culture of continuous growth, curiosity, and the pursuit of excellence in all areas of life.",
      color: "hot-pink"
    }
  ];

  return (
    <section id="about" className="py-20 bg-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Mission Statement */}
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-arvo font-bold text-white mb-8">
            Our <span className="gradient-text">Mission</span>
          </h2>
          <div className="max-w-4xl mx-auto">
            <p className="text-2xl md:text-3xl font-arvo text-white leading-relaxed mb-8">
              "To empower teenagers to discover and develop their{' '}
              <span className="gradient-text font-bold">God-given potential</span>{' '}
              through transformative leadership programs, meaningful mentorship, and impactful community engagement."
            </p>
            <div className="w-24 h-1 bg-gradient-to-r from-electric-blue to-bright-orange mx-auto"></div>
          </div>
        </div>

        {/* Story Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 mb-20">
          <div className="space-y-6">
            <h3 className="text-3xl font-arvo font-bold text-white mb-6">
              Our <span className="text-electric-blue">Story</span>
            </h3>
            <div className="space-y-4 text-gray-300 text-lg leading-relaxed">
              <p>
                NextGen Youth Movement was born from a simple yet powerful belief: every teenager has 
                untapped potential waiting to be unleashed. What started as a small community initiative 
                has grown into a transformative movement impacting hundreds of young lives.
              </p>
              <p>
                Our founders recognized that traditional youth programs often fell short of addressing 
                the real challenges and aspirations of today's teenagers. They envisioned something 
                different—a comprehensive approach that combines leadership development, practical skills, 
                spiritual growth, and community impact.
              </p>
              <p>
                Today, we stand as a beacon of hope and transformation, proving that when young people 
                are given the right tools, mentorship, and opportunities, they don't just change their 
                own lives—they change their communities and the world around them.
              </p>
            </div>
            
            <div className="flex items-center space-x-8 pt-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-electric-blue">2019</div>
                <div className="text-gray-400">Founded</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-neon-green">500+</div>
                <div className="text-gray-400">Lives Changed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-bright-orange">8</div>
                <div className="text-gray-400">Communities</div>
              </div>
            </div>
          </div>

          <div className="relative">
            <div className="relative z-10">
              <img
                src="https://images.unsplash.com/photo-1581090464777-f3220bbe1b8b?w=600&h=400&fit=crop"
                alt="Youth leadership in action"
                className="rounded-xl shadow-2xl w-full hover:scale-105 transition-transform duration-500"
              />
              
              {/* Overlay stats */}
              <div className="absolute bottom-4 left-4 right-4 bg-black/80 backdrop-blur-sm rounded-lg p-4">
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-xl font-bold text-electric-blue">95%</div>
                    <div className="text-xs text-gray-300">Success Rate</div>
                  </div>
                  <div>
                    <div className="text-xl font-bold text-neon-green">4.9/5</div>
                    <div className="text-xs text-gray-300">Rating</div>
                  </div>
                  <div>
                    <div className="text-xl font-bold text-bright-orange">100%</div>
                    <div className="text-xs text-gray-300">Free Programs</div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Background decoration */}
            <div className="absolute -top-4 -right-4 w-full h-full border-2 border-electric-blue/30 rounded-xl -z-10"></div>
            <div className="absolute top-4 right-4 w-24 h-24 bg-gradient-to-r from-neon-green/20 to-bright-orange/20 rounded-full blur-xl"></div>
          </div>
        </div>

        {/* Core Values */}
        <div className="text-center mb-12">
          <h3 className="text-3xl md:text-4xl font-arvo font-bold text-white mb-6">
            Our <span className="gradient-text">Core Values</span>
          </h3>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            These foundational principles guide everything we do and shape how we approach 
            youth development and leadership training.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {coreValues.map((value, index) => (
            <div
              key={value.title}
              className={`bg-gray-900/80 backdrop-blur-sm border border-gray-700 rounded-xl p-6 text-center hover-lift cursor-pointer relative overflow-hidden group ${
                activeValue === index ? 'ring-2 ring-white' : ''
              }`}
              onMouseEnter={() => setActiveValue(index)}
              onMouseLeave={() => setActiveValue(null)}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {/* Background gradient effect */}
              <div className={`absolute inset-0 bg-gradient-to-r from-${value.color}/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300`}></div>
              
              <div className="relative z-10">
                <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-${value.color} to-${value.color}/70 text-white mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  {value.icon}
                </div>
                
                <h4 className="text-xl font-arvo font-bold text-white mb-3 group-hover:text-white transition-colors duration-300">
                  {value.title}
                </h4>
                
                <p className="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors duration-300">
                  {value.description}
                </p>
              </div>

              {/* Bottom accent line */}
              <div className={`absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-${value.color} to-transparent transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500`}></div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-gray-900/50 to-black/50 backdrop-blur-sm border border-gray-700 rounded-2xl p-8 max-w-4xl mx-auto">
            <h3 className="text-2xl md:text-3xl font-arvo font-bold text-white mb-4">
              Ready to discover your potential?
            </h3>
            <p className="text-gray-300 mb-6 text-lg">
              Join hundreds of teenagers who have already started their transformation journey with NextGen Youth Movement.
            </p>
            <a
              href="#programs"
              className="btn-primary hover:shadow-electric-blue/50"
            >
              Explore Our Programs
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
import { useState, useEffect, useRef } from 'react';

interface StatItem {
  number: number;
  suffix: string;
  label: string;
  description: string;
  color: string;
  gradient: string;
}

const StatsSection = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [counters, setCounters] = useState([0, 0, 0, 0]);
  const sectionRef = useRef<HTMLDivElement>(null);

  const stats: StatItem[] = [
    {
      number: 500,
      suffix: '+',
      label: 'Youth Impacted',
      description: 'Lives transformed through our programs',
      color: 'electric-blue',
      gradient: 'from-electric-blue to-cyan-400'
    },
    {
      number: 12,
      suffix: '',
      label: 'Active Programs',
      description: 'Leadership and development initiatives',
      color: 'neon-green',
      gradient: 'from-neon-green to-green-400'
    },
    {
      number: 50,
      suffix: '+',
      label: 'Mentors',
      description: 'Dedicated leaders guiding youth',
      color: 'bright-orange',
      gradient: 'from-bright-orange to-orange-400'
    },
    {
      number: 8,
      suffix: '',
      label: 'Communities',
      description: 'Local areas we actively serve',
      color: 'hot-pink',
      gradient: 'from-hot-pink to-pink-400'
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
          // Start counter animations
          stats.forEach((stat, index) => {
            let startTime: number;
            const duration = 2000;
            const startValue = 0;
            const endValue = stat.number;
            
            const animate = (currentTime: number) => {
              if (!startTime) startTime = currentTime;
              const progress = Math.min((currentTime - startTime) / duration, 1);
              const easeOutCubic = 1 - Math.pow(1 - progress, 3);
              const currentValue = Math.floor(startValue + (endValue - startValue) * easeOutCubic);
              
              setCounters(prev => {
                const newCounters = [...prev];
                newCounters[index] = currentValue;
                return newCounters;
              });
              
              if (progress < 1) {
                requestAnimationFrame(animate);
              }
            };
            
            setTimeout(() => requestAnimationFrame(animate), index * 200);
          });
        }
      },
      { threshold: 0.5 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, [isVisible]);

  return (
    <section ref={sectionRef} className="py-20 bg-gradient-to-b from-black to-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-arvo font-bold text-white mb-6">
            Our <span className="gradient-text">Impact</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Real numbers, real transformation. See how NextGen Youth Movement is making a difference 
            in communities across the region.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <div
              key={stat.label}
              className="stats-card group cursor-pointer relative overflow-hidden"
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              {/* Animated background effect */}
              <div className={`absolute inset-0 bg-gradient-to-r ${stat.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-300`}></div>
              
              {/* Circular progress ring */}
              <div className="relative mb-6">
                <div className="circular-progress mx-auto">
                  <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 128 128">
                    <circle
                      cx="64"
                      cy="64"
                      r="56"
                      stroke="currentColor"
                      strokeWidth="8"
                      fill="none"
                      className="text-gray-700"
                    />
                    <circle
                      cx="64"
                      cy="64"
                      r="56"
                      stroke={`var(--${stat.color})`}
                      strokeWidth="8"
                      fill="none"
                      strokeLinecap="round"
                      strokeDasharray={`${2 * Math.PI * 56}`}
                      strokeDashoffset={`${2 * Math.PI * 56 * (1 - (isVisible ? 0.75 : 0))}`}
                      className={`text-${stat.color} transition-all duration-2000 ease-out`}
                      style={{ 
                        transitionDelay: `${index * 200}ms`,
                        filter: 'drop-shadow(0 0 10px currentColor)'
                      }}
                    />
                  </svg>
                  
                  {/* Center content */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center">
                      <div className={`text-3xl font-bold text-${stat.color} animate-counter-up`} style={{ animationDelay: `${index * 200}ms` }}>
                        {counters[index]}{stat.suffix}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="text-center">
                <h3 className="text-xl font-arvo font-bold text-white mb-2 group-hover:text-white transition-colors duration-300">
                  {stat.label}
                </h3>
                <p className="text-gray-400 text-sm group-hover:text-gray-300 transition-colors duration-300">
                  {stat.description}
                </p>
              </div>

              {/* Hover ripple effect */}
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
                <div className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-${stat.color} rounded-full animate-ripple`}></div>
              </div>
            </div>
          ))}
        </div>

        {/* Call to action */}
        <div className="text-center mt-16">
          <a
            href="#programs"
            className="btn-primary inline-flex items-center space-x-2 hover:shadow-electric-blue/50"
          >
            <span>Be Part of the Next Success Story</span>
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </a>
        </div>
      </div>
    </section>
  );
};

export default StatsSection;
import { useState } from 'react';
import { Book, Star, Contact, Calendar } from 'lucide-react';

interface Program {
  id: string;
  title: string;
  description: string;
  features: string[];
  color: string;
  gradient: string;
  icon: React.ReactNode;
  duration: string;
  nextSession: string;
}

const ProgramsSection = () => {
  const [activeProgram, setActiveProgram] = useState<string | null>(null);

  const programs: Program[] = [
    {
      id: 'leadership',
      title: 'Leadership Workshops',
      description: 'Intensive leadership development programs that build confidence, communication skills, and strategic thinking abilities.',
      features: ['Public Speaking Training', 'Team Building Exercises', 'Strategic Planning', 'Conflict Resolution'],
      color: 'electric-blue',
      gradient: 'from-electric-blue to-cyan-400',
      icon: <Star className="w-8 h-8" />,
      duration: '6 weeks',
      nextSession: 'March 15, 2024'
    },
    {
      id: 'community',
      title: 'Community Projects',
      description: 'Hands-on community service initiatives that develop civic responsibility and real-world problem-solving skills.',
      features: ['Community Assessment', 'Project Planning', 'Team Coordination', 'Impact Measurement'],
      color: 'neon-green',
      gradient: 'from-neon-green to-green-400',
      icon: <Contact className="w-8 h-8" />,
      duration: '8 weeks',
      nextSession: 'April 1, 2024'
    },
    {
      id: 'skills',
      title: 'Skills Development',
      description: 'Practical skill-building workshops covering digital literacy, financial planning, and career preparation.',
      features: ['Digital Literacy', 'Financial Planning', 'Resume Building', 'Interview Preparation'],
      color: 'bright-orange',
      gradient: 'from-bright-orange to-orange-400',
      icon: <Book className="w-8 h-8" />,
      duration: '4 weeks',
      nextSession: 'March 22, 2024'
    },
    {
      id: 'conferences',
      title: 'Youth Conferences',
      description: 'Annual conferences featuring inspiring speakers, networking opportunities, and skill-building workshops.',
      features: ['Keynote Speakers', 'Networking Sessions', 'Workshop Tracks', 'Awards Ceremony'],
      color: 'hot-pink',
      gradient: 'from-hot-pink to-pink-400',
      icon: <Calendar className="w-8 h-8" />,
      duration: '3 days',
      nextSession: 'June 10-12, 2024'
    }
  ];

  return (
    <section id="programs" className="py-20 bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-arvo font-bold text-white mb-6">
            Our <span className="gradient-text">Programs</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Comprehensive development programs designed to unlock potential and build tomorrow's leaders 
            through practical experience and mentorship.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {programs.map((program, index) => (
            <div
              key={program.id}
              className={`program-card group relative overflow-hidden cursor-pointer ${
                activeProgram === program.id ? 'ring-2 ring-white' : ''
              }`}
              onMouseEnter={() => setActiveProgram(program.id)}
              onMouseLeave={() => setActiveProgram(null)}
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              {/* Animated background */}
              <div className={`absolute inset-0 bg-gradient-to-r ${program.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-500`}></div>
              
              {/* Colored border effect */}
              <div className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${program.gradient} transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500`}></div>

              <div className="relative z-10">
                {/* Header */}
                <div className="flex items-center mb-6">
                  <div className={`p-3 rounded-lg bg-gradient-to-r ${program.gradient} text-white mr-4 group-hover:scale-110 transition-transform duration-300`}>
                    {program.icon}
                  </div>
                  <div>
                    <h3 className="text-2xl font-arvo font-bold text-white group-hover:text-white transition-colors duration-300">
                      {program.title}
                    </h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-400">
                      <span>Duration: {program.duration}</span>
                      <span>Next: {program.nextSession}</span>
                    </div>
                  </div>
                </div>

                {/* Description */}
                <p className="text-gray-300 mb-6 group-hover:text-gray-200 transition-colors duration-300">
                  {program.description}
                </p>

                {/* Features */}
                <div className="mb-6">
                  <h4 className="text-lg font-semibold text-white mb-3">What You'll Learn:</h4>
                  <div className="grid grid-cols-2 gap-2">
                    {program.features.map((feature, featureIndex) => (
                      <div
                        key={feature}
                        className="flex items-center text-gray-300 group-hover:text-gray-200 transition-all duration-300"
                        style={{ transitionDelay: `${featureIndex * 100}ms` }}
                      >
                        <div className={`w-2 h-2 rounded-full bg-${program.color} mr-2 group-hover:w-3 group-hover:h-3 transition-all duration-300`}></div>
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Action buttons */}
                <div className="flex space-x-4">
                  <button className={`flex-1 py-3 px-6 rounded-lg border-2 border-${program.color} text-${program.color} font-semibold hover:bg-${program.color} hover:text-white transition-all duration-300 group-hover:shadow-lg`}>
                    Learn More
                  </button>
                  <button className={`flex-1 py-3 px-6 rounded-lg bg-gradient-to-r ${program.gradient} text-white font-semibold hover:scale-105 transition-transform duration-300 shadow-lg hover:shadow-xl`}>
                    Apply Now
                  </button>
                </div>
              </div>

              {/* Hover particle effect */}
              {activeProgram === program.id && (
                <div className="absolute inset-0 pointer-events-none">
                  {[...Array(6)].map((_, i) => (
                    <div
                      key={i}
                      className={`absolute w-2 h-2 bg-${program.color} rounded-full animate-ping`}
                      style={{
                        top: `${Math.random() * 100}%`,
                        left: `${Math.random() * 100}%`,
                        animationDelay: `${i * 200}ms`,
                        animationDuration: '2s'
                      }}
                    ></div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Program highlights */}
        <div className="mt-16 text-center">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="text-3xl font-bold gradient-text mb-2">100%</div>
              <p className="text-gray-300">Program Completion Rate</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold gradient-text mb-2">95%</div>
              <p className="text-gray-300">Participant Satisfaction</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold gradient-text mb-2">80%</div>
              <p className="text-gray-300">Continue to Advanced Programs</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProgramsSection;

import { useState } from 'react';
import { Mail, Contact, Map, Calendar } from 'lucide-react';

const ContactSection = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    age: '',
    interest: '',
    message: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsSubmitting(false);
    setSubmitted(true);
    
    // Reset form after 3 seconds
    setTimeout(() => {
      setSubmitted(false);
      setFormData({ name: '', email: '', age: '', interest: '', message: '' });
    }, 3000);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <section id="contact" className="py-20 bg-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-arvo font-bold text-white mb-6">
            Get <span className="gradient-text">Connected</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Ready to start your transformation journey? We'd love to hear from you and help you 
            discover how NextGen Youth Movement can impact your life.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
          
          {/* Contact Form */}
          <div className="bg-gray-900/80 backdrop-blur-sm border border-gray-700 rounded-xl p-8">
            <h3 className="text-2xl font-arvo font-bold text-white mb-6">
              Join the <span className="text-electric-blue">Movement</span>
            </h3>
            
            {submitted ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gradient-to-r from-neon-green to-green-400 rounded-full flex items-center justify-center mx-auto mb-4 animate-scale-in">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <h4 className="text-xl font-bold text-white mb-2">Thank You!</h4>
                <p className="text-gray-300">We'll be in touch with you soon to discuss next steps.</p>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
                      Full Name *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      required
                      value={formData.name}
                      onChange={handleChange}
                      className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-electric-blue focus:border-transparent transition-all duration-300"
                      placeholder="Your full name"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      required
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-electric-blue focus:border-transparent transition-all duration-300"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="age" className="block text-sm font-medium text-gray-300 mb-2">
                      Age
                    </label>
                    <input
                      type="number"
                      id="age"
                      name="age"
                      min="13"
                      max="19"
                      value={formData.age}
                      onChange={handleChange}
                      className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-electric-blue focus:border-transparent transition-all duration-300"
                      placeholder="Your age"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="interest" className="block text-sm font-medium text-gray-300 mb-2">
                      Program Interest
                    </label>
                    <select
                      id="interest"
                      name="interest"
                      value={formData.interest}
                      onChange={handleChange}
                      className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-electric-blue focus:border-transparent transition-all duration-300"
                    >
                      <option value="">Select a program</option>
                      <option value="leadership">Leadership Workshops</option>
                      <option value="community">Community Projects</option>
                      <option value="skills">Skills Development</option>
                      <option value="mentorship">Mentorship Program</option>
                      <option value="conferences">Youth Conferences</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-300 mb-2">
                    Tell Us About Yourself
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    rows={4}
                    value={formData.message}
                    onChange={handleChange}
                    className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-electric-blue focus:border-transparent transition-all duration-300 resize-none"
                    placeholder="Share your goals, interests, or any questions you have about our programs..."
                  ></textarea>
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={`w-full py-4 px-6 rounded-lg font-semibold text-white transition-all duration-300 ${
                    isSubmitting
                      ? 'bg-gray-600 cursor-not-allowed'
                      : 'bg-gradient-to-r from-electric-blue to-bright-orange hover:scale-105 hover:shadow-xl'
                  }`}
                >
                  {isSubmitting ? (
                    <div className="flex items-center justify-center space-x-2">
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Submitting...</span>
                    </div>
                  ) : (
                    'Start My Journey'
                  )}
                </button>
              </form>
            )}
          </div>

          {/* Contact Info & Quick Actions */}
          <div className="space-y-8">
            
            {/* Contact Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div className="bg-gray-900/80 backdrop-blur-sm border border-gray-700 rounded-xl p-6 text-center hover-lift">
                <div className="w-12 h-12 bg-gradient-to-r from-electric-blue to-cyan-400 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Mail className="w-6 h-6 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-white mb-2">Email Us</h4>
                <p className="text-gray-400 text-sm mb-3">Get in touch directly</p>
                <a href="mailto:<EMAIL>" className="text-electric-blue hover:underline">
                  <EMAIL>
                </a>
              </div>

              <div className="bg-gray-900/80 backdrop-blur-sm border border-gray-700 rounded-xl p-6 text-center hover-lift">
                <div className="w-12 h-12 bg-gradient-to-r from-neon-green to-green-400 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Contact className="w-6 h-6 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-white mb-2">Call Us</h4>
                <p className="text-gray-400 text-sm mb-3">Speak with our team</p>
                <a href="tel:******-NGY-MOVE" className="text-neon-green hover:underline">
                  (555) NGY-MOVE
                </a>
              </div>

              <div className="bg-gray-900/80 backdrop-blur-sm border border-gray-700 rounded-xl p-6 text-center hover-lift">
                <div className="w-12 h-12 bg-gradient-to-r from-bright-orange to-orange-400 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Map className="w-6 h-6 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-white mb-2">Visit Us</h4>
                <p className="text-gray-400 text-sm mb-3">Our main office</p>
                <p className="text-bright-orange text-sm">
                  123 Leadership Ave<br />
                  Youth City, YC 12345
                </p>
              </div>

              <div className="bg-gray-900/80 backdrop-blur-sm border border-gray-700 rounded-xl p-6 text-center hover-lift">
                <div className="w-12 h-12 bg-gradient-to-r from-hot-pink to-pink-400 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Calendar className="w-6 h-6 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-white mb-2">Schedule</h4>
                <p className="text-gray-400 text-sm mb-3">Book a consultation</p>
                <button className="text-hot-pink hover:underline text-sm">
                  Book a Call
                </button>
              </div>
            </div>

            {/* FAQ Section */}
            <div className="bg-gray-900/80 backdrop-blur-sm border border-gray-700 rounded-xl p-8">
              <h3 className="text-xl font-arvo font-bold text-white mb-6">
                Frequently Asked <span className="text-neon-green">Questions</span>
              </h3>
              
              <div className="space-y-4">
                <div>
                  <h4 className="text-white font-semibold mb-2">Are your programs really free?</h4>
                  <p className="text-gray-400 text-sm">Yes! All our core programs are completely free thanks to community support and partnerships.</p>
                </div>
                
                <div>
                  <h4 className="text-white font-semibold mb-2">What age groups do you serve?</h4>
                  <p className="text-gray-400 text-sm">We primarily serve teenagers ages 13-19, with specialized programs for different age ranges.</p>
                </div>
                
                <div>
                  <h4 className="text-white font-semibold mb-2">How do I know which program is right for me?</h4>
                  <p className="text-gray-400 text-sm">Our team will work with you to assess your interests and goals to recommend the best fit.</p>
                </div>
              </div>
            </div>

            {/* Social Links */}
            <div className="text-center">
              <h4 className="text-lg font-semibold text-white mb-4">Follow Our Journey</h4>
              <div className="flex justify-center space-x-4">
                <a href="#" className="w-10 h-10 bg-electric-blue rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-300">
                  <span className="text-white font-bold text-sm">f</span>
                </a>
                <a href="#" className="w-10 h-10 bg-neon-green rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-300">
                  <span className="text-white font-bold text-sm">ig</span>
                </a>
                <a href="#" className="w-10 h-10 bg-bright-orange rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-300">
                  <span className="text-white font-bold text-sm">yt</span>
                </a>
                <a href="#" className="w-10 h-10 bg-hot-pink rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-300">
                  <span className="text-white font-bold text-sm">tw</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
import { useState, useEffect } from 'react';

const HeroSection = () => {
  const [displayText, setDisplayText] = useState('');
  const fullText = "Empowering Tomorrow's Leaders";
  
  useEffect(() => {
    let index = 0;
    const timer = setInterval(() => {
      if (index <= fullText.length) {
        setDisplayText(fullText.slice(0, index));
        index++;
      } else {
        clearInterval(timer);
      }
    }, 100);
    
    return () => clearInterval(timer);
  }, []);

  return (
    <section id="home" className="min-h-screen flex items-center justify-center relative overflow-hidden bg-black">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-64 h-64 rounded-full bg-gradient-to-r from-electric-blue/20 to-neon-green/20 blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 rounded-full bg-gradient-to-r from-bright-orange/20 to-hot-pink/20 blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 rounded-full border border-electric-blue/30 animate-spin" style={{ animationDuration: '20s' }}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 rounded-full border border-neon-green/30 animate-spin" style={{ animationDuration: '15s', animationDirection: 'reverse' }}></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
        <div className="animate-fade-in">
          <h1 className="text-5xl md:text-7xl lg:text-8xl font-arvo font-bold text-white mb-6 text-shadow">
            {displayText}
            <span className="gradient-text animate-pulse">|</span>
          </h1>
          
          <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto animate-fade-in" style={{ animationDelay: '2s' }}>
            Discover and develop your God-given potential through transformative leadership programs, 
            mentorship, and community engagement designed for the next generation.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center animate-fade-in" style={{ animationDelay: '3s' }}>
            <a 
              href="#programs" 
              className="btn-primary hover:animate-glow-pulse"
            >
              Join the Movement
            </a>
            <a 
              href="#about" 
              className="btn-secondary hover:scale-105"
            >
              Learn More
            </a>
          </div>

          {/* Scroll Indicator */}
          <div className="mt-16 animate-bounce">
            <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
              <div className="w-1 h-3 bg-electric-blue rounded-full mt-2 animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Action Elements */}
      <div className="absolute bottom-8 right-8 hidden lg:block">
        <div className="flex flex-col space-y-4">
          <div className="w-16 h-16 rounded-full bg-gradient-to-r from-electric-blue to-neon-green flex items-center justify-center text-white font-bold hover:scale-110 transition-transform duration-300 cursor-pointer animate-glow-pulse">
            500+
          </div>
          <p className="text-white text-sm text-center">Youth<br/>Impacted</p>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
import { useState } from 'react';
import { Star, Contact, Calendar, Book } from 'lucide-react';

interface Mentor {
  id: number;
  name: string;
  role: string;
  expertise: string[];
  image: string;
  bio: string;
  color: string;
}

const MentorshipSection = () => {
  const [activeMentor, setActiveMentor] = useState<number | null>(null);

  const mentors: Mentor[] = [
    {
      id: 1,
      name: "Sarah Johnson",
      role: "Leadership Coach",
      expertise: ["Public Speaking", "Team Leadership", "Goal Setting"],
      image: "https://images.unsplash.com/photo-1581090464777-f3220bbe1b8b?w=300&h=300&fit=crop&crop=face",
      bio: "Sarah has 10+ years of experience in youth development and has helped hundreds of teenagers discover their leadership potential.",
      color: "electric-blue"
    },
    {
      id: 2,
      name: "Marcus Rodriguez",
      role: "Community Impact Specialist",
      expertise: ["Project Management", "Social Innovation", "Civic Engagement"],
      image: "https://images.unsplash.com/photo-1506744038136-46273834b3fb?w=300&h=300&fit=crop&crop=center",
      bio: "Marcus leads our community projects and has facilitated over 50 successful youth-led community initiatives.",
      color: "neon-green"
    },
    {
      id: 3,
      name: "Dr. Emily Chen",
      role: "Skills Development Director",
      expertise: ["Career Preparation", "Digital Literacy", "Financial Planning"],
      image: "https://images.unsplash.com/photo-1518495973542-4542c06a5843?w=300&h=300&fit=crop&crop=face",
      bio: "Dr. Chen brings corporate experience and academic expertise to help youth develop practical life and career skills.",
      color: "bright-orange"
    },
    {
      id: 4,
      name: "James Thompson",
      role: "Spiritual Life Coordinator",
      expertise: ["Character Development", "Values Formation", "Purpose Discovery"],
      image: "https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?w=300&h=300&fit=crop&crop=center",
      bio: "James guides young people in discovering their purpose and developing strong character foundations.",
      color: "hot-pink"
    }
  ];

  const processSteps = [
    {
      step: "1",
      title: "Application",
      description: "Complete our simple application form to tell us about your goals and interests.",
      color: "electric-blue"
    },
    {
      step: "2",
      title: "Matching",
      description: "We carefully match you with a mentor based on your aspirations and personality.",
      color: "neon-green"
    },
    {
      step: "3",
      title: "Introduction",
      description: "Meet your mentor in a structured introduction session to establish goals and expectations.",
      color: "bright-orange"
    },
    {
      step: "4",
      title: "Growth Journey",
      description: "Engage in regular mentoring sessions, workshops, and practical application opportunities.",
      color: "hot-pink"
    }
  ];

  return (
    <section id="mentorship" className="py-20 bg-gradient-to-b from-gray-900 to-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-arvo font-bold text-white mb-6">
            <span className="gradient-text">Mentorship</span> That Transforms
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Connect with experienced mentors who are passionate about helping you discover your potential, 
            develop your skills, and make a lasting impact in your community.
          </p>
        </div>

        {/* Process Timeline */}
        <div className="mb-20">
          <h3 className="text-3xl font-arvo font-bold text-white text-center mb-12">
            Your Mentorship <span className="text-electric-blue">Journey</span>
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {processSteps.map((step, index) => (
              <div key={step.step} className="relative">
                {/* Connecting line (hidden on mobile) */}
                {index < processSteps.length - 1 && (
                  <div className="hidden lg:block absolute top-8 left-full w-full h-0.5 bg-gradient-to-r from-gray-600 to-gray-700 transform translate-x-4 z-0"></div>
                )}
                
                <div className="relative z-10 text-center">
                  <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-${step.color} to-${step.color}/70 text-white text-xl font-bold mb-4 hover:scale-110 transition-transform duration-300`}>
                    {step.step}
                  </div>
                  <h4 className="text-xl font-arvo font-bold text-white mb-3">{step.title}</h4>
                  <p className="text-gray-400 text-sm leading-relaxed">{step.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Meet Our Mentors */}
        <div className="mb-16">
          <h3 className="text-3xl font-arvo font-bold text-white text-center mb-12">
            Meet Our <span className="text-neon-green">Mentors</span>
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {mentors.map((mentor, index) => (
              <div
                key={mentor.id}
                className={`bg-gray-900/80 backdrop-blur-sm border border-gray-700 rounded-xl p-6 text-center hover-lift cursor-pointer relative overflow-hidden group ${
                  activeMentor === mentor.id ? 'ring-2 ring-white' : ''
                }`}
                onMouseEnter={() => setActiveMentor(mentor.id)}
                onMouseLeave={() => setActiveMentor(null)}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                {/* Background effect */}
                <div className={`absolute inset-0 bg-gradient-to-r from-${mentor.color}/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300`}></div>
                
                <div className="relative z-10">
                  {/* Profile Image */}
                  <div className="relative mb-4">
                    <img
                      src={mentor.image}
                      alt={mentor.name}
                      className="w-24 h-24 rounded-full mx-auto object-cover border-4 border-gray-700 group-hover:border-white transition-colors duration-300"
                    />
                    <div className={`absolute inset-0 rounded-full bg-gradient-to-r from-${mentor.color}/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300`}></div>
                  </div>
                  
                  <h4 className="text-xl font-arvo font-bold text-white mb-1">{mentor.name}</h4>
                  <p className={`text-${mentor.color} font-semibold mb-3`}>{mentor.role}</p>
                  
                  {/* Expertise Tags */}
                  <div className="flex flex-wrap justify-center gap-2 mb-4">
                    {mentor.expertise.map((skill) => (
                      <span
                        key={skill}
                        className="text-xs bg-gray-800 text-gray-300 px-2 py-1 rounded-full group-hover:bg-gray-700 transition-colors duration-300"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                  
                  {/* Bio (shown on hover) */}
                  <div className={`transition-all duration-300 ${activeMentor === mentor.id ? 'max-h-40 opacity-100' : 'max-h-0 opacity-0 overflow-hidden'}`}>
                    <p className="text-gray-400 text-sm leading-relaxed mb-4">{mentor.bio}</p>
                    <button className={`text-${mentor.color} text-sm font-semibold hover:underline`}>
                      Connect with {mentor.name.split(' ')[0]}
                    </button>
                  </div>
                </div>

                {/* Bottom accent */}
                <div className={`absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-${mentor.color} to-transparent transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500`}></div>
              </div>
            ))}
          </div>
        </div>

        {/* Success Stories */}
        <div className="mb-16">
          <h3 className="text-3xl font-arvo font-bold text-white text-center mb-12">
            <span className="text-bright-orange">Success</span> Stories
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-gray-900/80 backdrop-blur-sm border border-gray-700 rounded-xl p-8 hover-lift">
              <div className="flex items-start space-x-4">
                <img
                  src="https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=80&h=80&fit=crop&crop=face"
                  alt="Alex M."
                  className="w-16 h-16 rounded-full object-cover"
                />
                <div>
                  <h4 className="text-lg font-arvo font-bold text-white mb-2">Alex M., Age 17</h4>
                  <p className="text-gray-300 text-sm mb-4">
                    "My mentor Sarah helped me overcome my fear of public speaking. Now I'm leading 
                    youth council meetings and have been accepted to my dream college with a 
                    leadership scholarship!"
                  </p>
                  <div className="flex space-x-1">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 text-electric-blue fill-current" />
                    ))}
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gray-900/80 backdrop-blur-sm border border-gray-700 rounded-xl p-8 hover-lift">
              <div className="flex items-start space-x-4">
                <img
                  src="https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=80&h=80&fit=crop&crop=face"
                  alt="Maria S."
                  className="w-16 h-16 rounded-full object-cover"
                />
                <div>
                  <h4 className="text-lg font-arvo font-bold text-white mb-2">Maria S., Age 16</h4>
                  <p className="text-gray-300 text-sm mb-4">
                    "Through the mentorship program, I discovered my passion for community service. 
                    I've now started a food drive initiative that has helped over 200 families in our area."
                  </p>
                  <div className="flex space-x-1">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 text-neon-green fill-current" />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-gray-900/50 to-black/50 backdrop-blur-sm border border-gray-700 rounded-2xl p-8 max-w-4xl mx-auto">
            <h3 className="text-2xl md:text-3xl font-arvo font-bold text-white mb-4">
              Ready to find your mentor?
            </h3>
            <p className="text-gray-300 mb-6 text-lg">
              Take the first step towards unlocking your potential with personalized mentorship 
              designed to help you succeed.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="#contact"
                className="btn-primary hover:shadow-electric-blue/50"
              >
                Apply for Mentorship
              </a>
              <button className="btn-secondary hover:scale-105">
                Learn More
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default MentorshipSection;
import * as React from "react"

const MOBILE_BREAKPOINT = 768

export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)

  React.useEffect(() => {
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)
    const onChange = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    }
    mql.addEventListener("change", onChange)
    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    return () => mql.removeEventListener("change", onChange)
  }, [])

  return !!isMobile
}
import * as React from "react"

import type {
  ToastActionElement,
  ToastProps,
} from "@/components/ui/toast"

const TOAST_LIMIT = 1
const TOAST_REMOVE_DELAY = 1000000

type ToasterToast = ToastProps & {
  id: string
  title?: React.ReactNode
  description?: React.ReactNode
  action?: ToastActionElement
}

const actionTypes = {
  ADD_TOAST: "ADD_TOAST",
  UPDATE_TOAST: "UPDATE_TOAST",
  DISMISS_TOAST: "DISMISS_TOAST",
  REMOVE_TOAST: "REMOVE_TOAST",
} as const

let count = 0

function genId() {
  count = (count + 1) % Number.MAX_SAFE_INTEGER
  return count.toString()
}

type ActionType = typeof actionTypes

type Action =
  | {
      type: ActionType["ADD_TOAST"]
      toast: ToasterToast
    }
  | {
      type: ActionType["UPDATE_TOAST"]
      toast: Partial<ToasterToast>
    }
  | {
      type: ActionType["DISMISS_TOAST"]
      toastId?: ToasterToast["id"]
    }
  | {
      type: ActionType["REMOVE_TOAST"]
      toastId?: ToasterToast["id"]
    }

interface State {
  toasts: ToasterToast[]
}

const toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()

const addToRemoveQueue = (toastId: string) => {
  if (toastTimeouts.has(toastId)) {
    return
  }

  const timeout = setTimeout(() => {
    toastTimeouts.delete(toastId)
    dispatch({
      type: "REMOVE_TOAST",
      toastId: toastId,
    })
  }, TOAST_REMOVE_DELAY)

  toastTimeouts.set(toastId, timeout)
}

export const reducer = (state: State, action: Action): State => {
  switch (action.type) {
    case "ADD_TOAST":
      return {
        ...state,
        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),
      }

    case "UPDATE_TOAST":
      return {
        ...state,
        toasts: state.toasts.map((t) =>
          t.id === action.toast.id ? { ...t, ...action.toast } : t
        ),
      }

    case "DISMISS_TOAST": {
      const { toastId } = action

      // ! Side effects ! - This could be extracted into a dismissToast() action,
      // but I'll keep it here for simplicity
      if (toastId) {
        addToRemoveQueue(toastId)
      } else {
        state.toasts.forEach((toast) => {
          addToRemoveQueue(toast.id)
        })
      }

      return {
        ...state,
        toasts: state.toasts.map((t) =>
          t.id === toastId || toastId === undefined
            ? {
                ...t,
                open: false,
              }
            : t
        ),
      }
    }
    case "REMOVE_TOAST":
      if (action.toastId === undefined) {
        return {
          ...state,
          toasts: [],
        }
      }
      return {
        ...state,
        toasts: state.toasts.filter((t) => t.id !== action.toastId),
      }
  }
}

const listeners: Array<(state: State) => void> = []

let memoryState: State = { toasts: [] }

function dispatch(action: Action) {
  memoryState = reducer(memoryState, action)
  listeners.forEach((listener) => {
    listener(memoryState)
  })
}

type Toast = Omit<ToasterToast, "id">

function toast({ ...props }: Toast) {
  const id = genId()

  const update = (props: ToasterToast) =>
    dispatch({
      type: "UPDATE_TOAST",
      toast: { ...props, id },
    })
  const dismiss = () => dispatch({ type: "DISMISS_TOAST", toastId: id })

  dispatch({
    type: "ADD_TOAST",
    toast: {
      ...props,
      id,
      open: true,
      onOpenChange: (open) => {
        if (!open) dismiss()
      },
    },
  })

  return {
    id: id,
    dismiss,
    update,
  }
}

function useToast() {
  const [state, setState] = React.useState<State>(memoryState)

  React.useEffect(() => {
    listeners.push(setState)
    return () => {
      const index = listeners.indexOf(setState)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }, [state])

  return {
    ...state,
    toast,
    dismiss: (toastId?: string) => dispatch({ type: "DISMISS_TOAST", toastId }),
  }
}

export { useToast, toast }
