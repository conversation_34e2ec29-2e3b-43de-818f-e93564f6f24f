'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Target, 
  Users, 
  Heart, 
  Lightbulb, 
  Award, 
  Calendar,
  Filter,
  Search
} from 'lucide-react';
import ProgramCard from '../ui/ProgramCard';
import useScrollReveal from '../../lib/hooks/useScrollReveal';

const ProgramsPage = () => {
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const heroReveal = useScrollReveal({ direction: 'up', delay: 0.2 });
  const programsReveal = useScrollReveal({ direction: 'up', delay: 0.4 });

  const programs = [
    {
      title: 'Youth Leadership Conference',
      description: 'Annual conference bringing together young leaders from across the region to develop leadership skills, network with peers, and learn from experienced mentors.',
      icon: <Target size={32} />,
      color: 'electric-blue' as const,
      href: '/programs/youth-conferences',
      duration: '3 Days',
      participants: '200+ Youth',
      nextSession: 'July 15-17, 2024',
      category: 'leadership',
      featured: true,
    },
    {
      title: 'Mentorship Program',
      description: 'One-on-one mentoring connecting experienced leaders with emerging young talents for personal and professional development.',
      icon: <Users size={32} />,
      color: 'neon-green' as const,
      href: '/mentorship',
      duration: '6 Months',
      participants: '50+ Pairs',
      nextSession: 'Rolling Enrollment',
      category: 'mentorship',
      featured: true,
    },
    {
      title: 'Community Impact Projects',
      description: 'Hands-on service projects that make a real difference in local communities while developing leadership and teamwork skills.',
      icon: <Heart size={32} />,
      color: 'bright-orange' as const,
      href: '/programs/community-projects',
      duration: 'Ongoing',
      participants: '100+ Volunteers',
      nextSession: 'Monthly',
      category: 'community',
    },
    {
      title: 'Skills Development Workshops',
      description: 'Practical workshops covering essential life and professional skills including communication, financial literacy, and career planning.',
      icon: <Lightbulb size={32} />,
      color: 'hot-pink' as const,
      href: '/programs/skills-development',
      duration: '4-8 Hours',
      participants: '25-30 per session',
      nextSession: 'Weekly',
      category: 'skills',
    },
    {
      title: 'Leadership Academy',
      description: 'Intensive 12-week program designed to develop advanced leadership capabilities and prepare youth for significant leadership roles.',
      icon: <Award size={32} />,
      color: 'electric-purple' as const,
      href: '/programs/leadership-academy',
      duration: '12 Weeks',
      participants: '20 Selected Youth',
      nextSession: 'September 2024',
      category: 'leadership',
    },
    {
      title: 'Peer Support Groups',
      description: 'Safe spaces for young people to connect, share experiences, and support each other through challenges and growth.',
      icon: <Users size={32} />,
      color: 'cyber-red' as const,
      href: '/programs/peer-support',
      duration: 'Ongoing',
      participants: '8-12 per group',
      nextSession: 'Bi-weekly',
      category: 'community',
    },
  ];

  const filters = [
    { id: 'all', label: 'All Programs', count: programs.length },
    { id: 'leadership', label: 'Leadership', count: programs.filter(p => p.category === 'leadership').length },
    { id: 'mentorship', label: 'Mentorship', count: programs.filter(p => p.category === 'mentorship').length },
    { id: 'community', label: 'Community', count: programs.filter(p => p.category === 'community').length },
    { id: 'skills', label: 'Skills', count: programs.filter(p => p.category === 'skills').length },
  ];

  const filteredPrograms = programs.filter(program => {
    const matchesFilter = selectedFilter === 'all' || program.category === selectedFilter;
    const matchesSearch = program.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         program.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-dark-bg via-dark-bg to-neon-green/10 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            ref={heroReveal.ref}
            initial={heroReveal.initial}
            animate={heroReveal.controls}
            className="text-center"
          >
            <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
              Our <span className="text-neon-green">Programs</span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Discover opportunities to grow, learn, and make a difference through our 
              comprehensive youth development programs designed to unlock your potential.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Filter and Search Section */}
      <section className="py-12 bg-dark-bg border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">
            {/* Search */}
            <div className="relative w-full lg:w-96">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <input
                type="text"
                placeholder="Search programs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 bg-dark-gray border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:border-electric-blue focus:outline-none transition-colors duration-300"
              />
            </div>

            {/* Filters */}
            <div className="flex items-center space-x-4">
              <Filter className="text-gray-400" size={20} />
              <div className="flex flex-wrap gap-2">
                {filters.map((filter) => (
                  <motion.button
                    key={filter.id}
                    onClick={() => setSelectedFilter(filter.id)}
                    className={`px-4 py-2 rounded-xl font-medium transition-all duration-300 ${
                      selectedFilter === filter.id
                        ? 'bg-electric-blue text-dark-bg'
                        : 'bg-dark-gray text-gray-300 hover:bg-gray-600'
                    }`}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {filter.label} ({filter.count})
                  </motion.button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Programs Grid */}
      <section className="py-20 bg-dark-bg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            ref={programsReveal.ref}
            initial={programsReveal.initial}
            animate={programsReveal.controls}
            className="mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white text-center mb-4">
              {selectedFilter === 'all' ? 'All Programs' : filters.find(f => f.id === selectedFilter)?.label}
            </h2>
            <p className="text-gray-300 text-center">
              {filteredPrograms.length} program{filteredPrograms.length !== 1 ? 's' : ''} available
            </p>
          </motion.div>

          {filteredPrograms.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredPrograms.map((program, index) => (
                <motion.div
                  key={program.title}
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <ProgramCard {...program} />
                </motion.div>
              ))}
            </div>
          ) : (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-16"
            >
              <div className="w-24 h-24 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
                <Search className="text-gray-400" size={32} />
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">No Programs Found</h3>
              <p className="text-gray-300 mb-6">
                Try adjusting your search terms or filters to find what you're looking for.
              </p>
              <motion.button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedFilter('all');
                }}
                className="px-6 py-3 bg-electric-blue text-dark-bg font-semibold rounded-xl hover:shadow-lg transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Clear Filters
              </motion.button>
            </motion.div>
          )}
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-gradient-to-r from-electric-blue/10 to-bright-orange/10">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="space-y-8"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white">
              Ready to <span className="text-bright-orange">Get Started?</span>
            </h2>
            <p className="text-xl text-gray-300 leading-relaxed">
              Don't see exactly what you're looking for? We're always developing new programs 
              based on the needs and interests of our community. Reach out to learn more!
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <motion.a
                href="/contact"
                className="inline-flex items-center justify-center px-8 py-4 bg-electric-blue text-dark-bg font-bold rounded-2xl text-lg hover:shadow-2xl hover:shadow-electric-blue/50 transition-all duration-300"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                <Calendar className="mr-2" size={20} />
                Schedule a Meeting
              </motion.a>
              <motion.a
                href="/mentorship"
                className="inline-flex items-center justify-center px-8 py-4 border-2 border-neon-green text-neon-green font-bold rounded-2xl text-lg hover:bg-neon-green hover:text-dark-bg transition-all duration-300"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                <Users className="mr-2" size={20} />
                Find a Mentor
              </motion.a>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default ProgramsPage;
